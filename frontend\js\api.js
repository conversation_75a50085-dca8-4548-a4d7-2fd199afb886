/**
 * API请求处理模块
 * 提供统一的API请求接口和错误处理
 */

class APIClient {
    constructor() {
        this.timeout = CONFIG.API.TIMEOUT;
        this.retryCount = CONFIG.API.RETRY_COUNT;
        this.retryDelay = CONFIG.API.RETRY_DELAY;

        // 请求拦截器
        this.requestInterceptors = [];
        this.responseInterceptors = [];

        // 添加默认拦截器
        this.addDefaultInterceptors();
    }

    /**
     * 动态获取API基础URL
     */
    getBaseURL() {
        // 优先使用保存的服务器地址
        try {
            const savedAuth = localStorage.getItem('fileShareAuth');
            if (savedAuth) {
                const authData = JSON.parse(savedAuth);
                if (authData.serverUrl) {
                    const baseUrl = `${authData.serverUrl}/api`;
                    CONFIG.log('debug', `使用保存的服务器地址: ${baseUrl}`);
                    return baseUrl;
                }
            }
        } catch (e) {
            CONFIG.log('warn', '读取保存的服务器地址失败:', e);
        }

        // 使用默认配置 - 确保总是返回有效的URL
        const defaultUrl = CONFIG.API.DEFAULT_SERVER_URL + '/api';
        CONFIG.log('debug', `使用默认服务器地址: ${defaultUrl}`);
        return defaultUrl;
    }
    
    /**
     * 添加默认拦截器
     */
    addDefaultInterceptors() {
        // 请求拦截器 - 添加通用头部和认证
        this.addRequestInterceptor((config) => {
            config.headers = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                ...config.headers
            };

            // 添加认证头部
            const authData = localStorage.getItem('fileShareAuth');
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    if (auth.token) {
                        config.headers['Authorization'] = `Bearer ${auth.token}`;
                    }
                } catch (e) {
                    CONFIG.log('warn', 'Invalid auth data in localStorage');
                }
            }

            // 调试日志
            if (CONFIG.DEBUG.SHOW_API_CALLS) {
                CONFIG.log('debug', `API Request: ${config.method} ${config.url}`, config);
            }

            return config;
        });
        
        // 响应拦截器 - 统一错误处理
        this.addResponseInterceptor(
            (response) => {
                // 调试日志
                if (CONFIG.DEBUG.SHOW_API_CALLS) {
                    CONFIG.log('debug', `API Response: ${response.status}`, response);
                }
                
                return response;
            },
            (error) => {
                CONFIG.log('error', 'API Error:', error);
                return Promise.reject(this.handleError(error));
            }
        );
    }
    
    /**
     * 添加请求拦截器
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }
    
    /**
     * 添加响应拦截器
     */
    addResponseInterceptor(onFulfilled, onRejected) {
        this.responseInterceptors.push({ onFulfilled, onRejected });
    }
    
    /**
     * 处理请求配置
     */
    processRequestConfig(config) {
        let processedConfig = { ...config };
        
        for (const interceptor of this.requestInterceptors) {
            processedConfig = interceptor(processedConfig);
        }
        
        return processedConfig;
    }
    
    /**
     * 处理响应
     */
    async processResponse(responsePromise) {
        try {
            let response = await responsePromise;
            
            for (const interceptor of this.responseInterceptors) {
                if (interceptor.onFulfilled) {
                    response = await interceptor.onFulfilled(response);
                }
            }
            
            return response;
        } catch (error) {
            for (const interceptor of this.responseInterceptors) {
                if (interceptor.onRejected) {
                    error = await interceptor.onRejected(error);
                }
            }
            throw error;
        }
    }
    
    /**
     * 基础请求方法
     */
    async request(config) {
        const processedConfig = this.processRequestConfig({
            timeout: this.timeout,
            ...config
        });

        const url = processedConfig.url.startsWith('http')
            ? processedConfig.url
            : `${this.getBaseURL()}${processedConfig.url}`;

        const fetchConfig = {
            method: processedConfig.method || 'GET',
            headers: processedConfig.headers,
            signal: this.createAbortSignal(processedConfig.timeout)
        };

        // 添加请求体
        if (processedConfig.data) {
            if (processedConfig.data instanceof FormData) {
                fetchConfig.body = processedConfig.data;
                // 删除Content-Type，让浏览器自动设置
                delete fetchConfig.headers['Content-Type'];
            } else {
                fetchConfig.body = JSON.stringify(processedConfig.data);
            }
        }

        // 添加查询参数
        const finalUrl = this.buildURL(url, processedConfig.params);

        return this.processResponse(
            this.fetchWithRetry(finalUrl, fetchConfig, this.retryCount)
        );
    }
    
    /**
     * 创建超时信号
     */
    createAbortSignal(timeout) {
        const controller = new AbortController();
        setTimeout(() => controller.abort(), timeout);
        return controller.signal;
    }
    
    /**
     * 构建URL
     */
    buildURL(url, params) {
        if (!params) return url;
        
        const searchParams = new URLSearchParams();
        for (const [key, value] of Object.entries(params)) {
            if (value !== null && value !== undefined) {
                searchParams.append(key, value);
            }
        }
        
        const queryString = searchParams.toString();
        return queryString ? `${url}?${queryString}` : url;
    }
    
    /**
     * 带重试的fetch
     */
    async fetchWithRetry(url, config, retryCount) {
        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 尝试解析JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const data = await response.json();
                return { ...response, data };
            }
            
            return response;
        } catch (error) {
            if (retryCount > 0 && this.shouldRetry(error)) {
                CONFIG.log('warn', `Request failed, retrying... (${retryCount} attempts left)`, error);
                await this.delay(this.retryDelay);
                return this.fetchWithRetry(url, config, retryCount - 1);
            }
            throw error;
        }
    }
    
    /**
     * 判断是否应该重试
     */
    shouldRetry(error) {
        // 网络错误或超时错误可以重试
        return error.name === 'TypeError' || 
               error.name === 'AbortError' ||
               (error.message && error.message.includes('fetch'));
    }
    
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 错误处理
     */
    handleError(error) {
        let message = CONFIG.ERROR_MESSAGES.UNKNOWN_ERROR;
        
        if (error.name === 'AbortError') {
            message = '请求超时，请重试';
        } else if (error.name === 'TypeError') {
            message = CONFIG.ERROR_MESSAGES.NETWORK_ERROR;
        } else if (error.message) {
            if (error.message.includes('404')) {
                message = CONFIG.ERROR_MESSAGES.FILE_NOT_FOUND;
            } else if (error.message.includes('403')) {
                message = CONFIG.ERROR_MESSAGES.PERMISSION_DENIED;
            } else if (error.message.includes('500')) {
                message = CONFIG.ERROR_MESSAGES.SERVER_ERROR;
            }
        }
        
        return {
            ...error,
            userMessage: message
        };
    }
    
    // HTTP方法快捷方式
    get(url, config = {}) {
        return this.request({ ...config, method: 'GET', url });
    }
    
    post(url, data, config = {}) {
        return this.request({ ...config, method: 'POST', url, data });
    }
    
    put(url, data, config = {}) {
        return this.request({ ...config, method: 'PUT', url, data });
    }
    
    delete(url, config = {}) {
        return this.request({ ...config, method: 'DELETE', url });
    }
    
    patch(url, data, config = {}) {
        return this.request({ ...config, method: 'PATCH', url, data });
    }
}

// 创建API客户端实例
const api = new APIClient();

/**
 * 系统API
 */
class SystemAPI {
    /**
     * 获取系统信息
     */
    static async getSystemInfo() {
        try {
            const response = await api.get('/system/info');
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get system info:', error);
            throw error;
        }
    }

    /**
     * 获取系统状态
     */
    static async getSystemStatus() {
        try {
            const response = await api.get('/server/status');
            return response;
        } catch (error) {
            CONFIG.log('error', 'Failed to get system status:', error);
            throw error;
        }
    }

    /**
     * 获取在线用户列表（管理员）
     */
    static async getOnlineUsers() {
        try {
            const response = await api.get('/system/online-users');
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get online users:', error);
            throw error;
        }
    }

    /**
     * 获取统计信息（管理员）
     */
    static async getStatistics() {
        try {
            const response = await api.get('/admin/stats');
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get statistics:', error);
            throw error;
        }
    }

    /**
     * 获取通知列表
     */
    static async getNotifications() {
        try {
            // 暂时返回模拟数据，避免错误
            return {
                success: true,
                notifications: []
            };
        } catch (error) {
            CONFIG.log('error', 'Failed to get notifications:', error);
            throw error;
        }
    }

    /**
     * 发送通知
     */
    static async sendNotification(notification) {
        try {
            // 暂时返回成功，避免错误
            return {
                success: true
            };
        } catch (error) {
            CONFIG.log('error', 'Failed to send notification:', error);
            throw error;
        }
    }
}

/**
 * 用户API
 */
class UserAPI {
    /**
     * 获取用户信息
     */
    static async getUserInfo() {
        try {
            const response = await api.get('/user/info');
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get user info:', error);
            throw error;
        }
    }

    /**
     * 用户登出
     */
    static async logout() {
        try {
            const response = await api.post('/auth/logout');
            return response;
        } catch (error) {
            CONFIG.log('error', 'Failed to logout:', error);
            throw error;
        }
    }
}

// 全局可用
window.SystemAPI = SystemAPI;
window.UserAPI = UserAPI;

/**
 * 文件API
 */
const FileAPI = {
    /**
     * 获取文件列表
     */
    async getFiles(folderId = null, params = {}) {
        const response = await api.get(CONFIG.API.ENDPOINTS.FILES, {
            params: { folder_id: folderId, ...params }
        });
        return response.data;
    },
    
    /**
     * 上传文件
     */
    async uploadFile(file, folderId = null, onProgress = null) {
        const formData = new FormData();
        formData.append('file', file);
        if (folderId) {
            formData.append('folder_id', folderId);
        }
        
        const response = await api.post(CONFIG.API.ENDPOINTS.UPLOAD, formData, {
            onUploadProgress: onProgress
        });
        return response.data;
    },
    
    /**
     * 下载文件
     */
    async downloadFile(fileId) {
        const endpoint = CONFIG.API.ENDPOINTS.DOWNLOAD.replace('{id}', fileId);
        const response = await api.get(endpoint, {
            responseType: 'blob'
        });
        return response;
    },

    /**
     * 单文件下载（压缩包形式）
     */
    async downloadSingleFile(fileId) {
        const endpoint = CONFIG.API.ENDPOINTS.DOWNLOAD_SINGLE.replace('{id}', fileId);
        const response = await api.post(endpoint);
        return response.data;
    },

    /**
     * 批量下载文件
     */
    async downloadBatchFiles(fileIds) {
        const response = await api.post(CONFIG.API.ENDPOINTS.DOWNLOAD_BATCH, {
            file_ids: fileIds
        });
        return response.data;
    },

    /**
     * 批量下载文件（返回blob）
     */
    async batchDownload(fileIds) {
        const response = await api.post(CONFIG.API.ENDPOINTS.DOWNLOAD_BATCH, {
            file_ids: fileIds
        }, {
            responseType: 'blob'
        });
        return response;
    },

    /**
     * 下载文件夹（返回blob）
     */
    async downloadFolder(folderId) {
        const endpoint = CONFIG.API.ENDPOINTS.DOWNLOAD_FOLDER.replace('{id}', folderId);
        const response = await api.get(endpoint, {
            responseType: 'blob'
        });
        return response;
    },

    /**
     * 获取文件预览URL
     */
    getPreviewURL(fileId) {
        const endpoint = CONFIG.API.ENDPOINTS.PREVIEW.replace('{id}', fileId);
        return `${api.getBaseURL()}${endpoint}`;
    },

    /**
     * 获取缩略图URL
     */
    getThumbnailURL(fileId, size = 'medium') {
        const endpoint = CONFIG.API.ENDPOINTS.THUMBNAIL.replace('{id}', fileId);
        const baseUrl = `${api.getBaseURL()}${endpoint}?size=${size}`;

        // 临时不发送token，用于调试缩略图功能
        // TODO: 修复认证问题后重新启用token
        return baseUrl;

        // 添加认证token到URL参数中（因为img标签无法设置headers）
        // const authData = localStorage.getItem('fileShareAuth');
        // if (authData) {
        //     try {
        //         const auth = JSON.parse(authData);
        //         if (auth.token) {
        //             return `${baseUrl}&token=${encodeURIComponent(auth.token)}`;
        //         }
        //     } catch (e) {
        //         CONFIG.log('warn', 'Invalid auth data in localStorage');
        //     }
        // }
        //
        // return baseUrl;
    },

    /**
     * 申请下载密码
     */
    async requestDownloadPassword(fileId, reason = '') {
        const response = await api.post(CONFIG.API.ENDPOINTS.DOWNLOAD_PASSWORD, {
            file_id: fileId,
            reason: reason
        });
        return response.data;
    }
};

/**
 * 文件夹API
 */
const FolderAPI = {
    /**
     * 获取共享文件夹列表
     */
    async getSharedFolders() {
        const response = await api.get(CONFIG.API.ENDPOINTS.FOLDERS);
        return response.data;
    }
};

/**
 * 搜索API
 */
const SearchAPI = {
    /**
     * 搜索文件
     */
    async search(query, type = 'text', params = {}) {
        const response = await api.post(CONFIG.API.ENDPOINTS.SEARCH, {
            query: query,
            type: type,
            ...params
        });
        return response.data;
    }
};

/**
 * 收藏API
 */
const FavoriteAPI = {
    /**
     * 获取用户收藏列表
     */
    async getFavorites(page = 1, pageSize = 50, folderId = null) {
        const params = { page, page_size: pageSize };
        if (folderId) {
            params.folder_id = folderId;
        }

        const response = await api.get(CONFIG.API.ENDPOINTS.FAVORITES, { params });
        return response.data;
    },

    /**
     * 切换文件收藏状态
     */
    async toggleFavorite(fileId, notes = '') {
        const response = await api.post(CONFIG.API.ENDPOINTS.FAVORITES_TOGGLE, {
            file_id: fileId,
            notes: notes
        });
        return response.data;
    },

    /**
     * 批量检查文件收藏状态
     */
    async checkFavoriteStatus(fileIds) {
        const response = await api.post(CONFIG.API.ENDPOINTS.FAVORITES_STATUS, {
            file_ids: fileIds
        });
        return response.data;
    },

    /**
     * 获取收藏统计信息
     */
    async getFavoriteStats() {
        const response = await api.get(CONFIG.API.ENDPOINTS.FAVORITES_STATS);
        return response.data;
    },

    /**
     * 添加收藏
     */
    async addFavorite(fileId, notes = '') {
        return await this.toggleFavorite(fileId, notes);
    },

    /**
     * 取消收藏
     */
    async removeFavorite(fileId) {
        return await this.toggleFavorite(fileId);
    }
};

/**
 * 统计API
 */
const StatsAPI = {
    /**
     * 获取存储统计
     */
    async getStorageStats() {
        const response = await api.get(CONFIG.API.ENDPOINTS.STORAGE);
        return response.data;
    }
};

/**
 * 配置管理API
 */
class ConfigAPI {
    /**
     * 获取公开配置（前端使用）
     */
    static async getPublicConfig() {
        try {
            const response = await api.get(CONFIG.API.ENDPOINTS.PUBLIC_CONFIG);
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get public config:', error);
            throw error;
        }
    }

    /**
     * 获取系统配置（管理员）
     */
    static async getSystemConfig() {
        try {
            const response = await api.get(CONFIG.API.ENDPOINTS.ADMIN_CONFIG);
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get system config:', error);
            throw error;
        }
    }

    /**
     * 更新系统配置（管理员）
     */
    static async updateSystemConfig(config) {
        try {
            const response = await api.put(CONFIG.API.ENDPOINTS.ADMIN_CONFIG, config);
            return response;
        } catch (error) {
            CONFIG.log('error', 'Failed to update system config:', error);
            throw error;
        }
    }

    /**
     * 应用配置到前端
     */
    static applyConfig(config) {
        try {
            // 更新API基础URL
            if (config.server) {
                const newBaseUrl = `http://${config.server.host}:${config.server.port}/api`;
                api.baseURL = newBaseUrl;
                CONFIG.API.DEFAULT_SERVER_URL = `http://${config.server.host}:${config.server.port}`;
            }

            // 更新文件配置
            if (config.file_share) {
                CONFIG.FILE.ALLOWED_EXTENSIONS = config.file_share.allowed_extensions || [];
                CONFIG.FILE.MAX_FILE_SIZE = config.file_share.max_file_size || 1073741824;
                CONFIG.FILE.THUMBNAIL_SIZES = config.file_share.thumbnail_sizes || {};
            }

            // 更新下载配置
            if (config.download) {
                CONFIG.DOWNLOAD = {
                    ...CONFIG.DOWNLOAD,
                    ...config.download
                };
            }

            // 更新搜索配置
            if (config.search) {
                CONFIG.SEARCH = {
                    ...CONFIG.SEARCH,
                    ...config.search
                };
            }

            // 更新通知配置
            if (config.notifications) {
                CONFIG.NOTIFICATIONS = {
                    ...CONFIG.NOTIFICATIONS,
                    ...config.notifications
                };
            }

            // 保存配置到本地存储
            localStorage.setItem('system_config', JSON.stringify(config));

            CONFIG.log('info', 'Configuration applied successfully');

        } catch (error) {
            CONFIG.log('error', 'Failed to apply configuration:', error);
            throw error;
        }
    }

    /**
     * 从本地存储加载配置
     */
    static loadLocalConfig() {
        try {
            const savedConfig = localStorage.getItem('system_config');
            if (savedConfig) {
                const config = JSON.parse(savedConfig);
                this.applyConfig(config);
                return config;
            }
        } catch (error) {
            CONFIG.log('warn', 'Failed to load local config:', error);
        }
        return null;
    }

    /**
     * 初始化配置（应用启动时调用）
     */
    static async initializeConfig() {
        try {
            // 先尝试加载本地配置
            this.loadLocalConfig();

            // 然后获取最新的服务器配置
            const serverConfig = await this.getPublicConfig();
            if (serverConfig) {
                this.applyConfig(serverConfig);
            }

            return serverConfig;
        } catch (error) {
            CONFIG.log('warn', 'Failed to initialize config from server, using local config');
            return this.loadLocalConfig();
        }
    }
}

// 导出API
window.api = api;
window.FileAPI = FileAPI;
window.FolderAPI = FolderAPI;
window.SearchAPI = SearchAPI;
window.FavoriteAPI = FavoriteAPI;
window.SystemAPI = SystemAPI;
window.UserAPI = UserAPI;
window.StatsAPI = StatsAPI;
window.ConfigAPI = ConfigAPI;
