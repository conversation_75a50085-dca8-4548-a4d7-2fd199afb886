2025-06-07 20:36:13 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-07 22:32:48 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-07 22:38:18 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-07 23:22:46 - APIServer - ERROR - 登录失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-07 23:52:30 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 00:17:45 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 00:36:26 - APIServer - ERROR - 获取文件列表失败: 'FileService' object has no attribute 'get_root_files'
2025-06-08 00:36:29 - APIServer - ERROR - 获取文件列表失败: 'FileService' object has no attribute 'get_root_files'
2025-06-08 00:36:30 - APIServer - ERROR - 获取文件列表失败: 'FileService' object has no attribute 'get_root_files'
2025-06-08 00:37:43 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:37:43 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:21 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:21 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:34 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:34 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:35 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:35 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:48:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:48:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:50:30 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:50:30 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:58:19 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:58:19 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:59:26 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:59:26 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:01:37 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:01:38 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:03:59 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:03:59 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:04:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:04:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:09:14 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 01:17:04 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 01:55:58 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 02:14:57 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 17:24:15 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 17:28:17 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 17:29:08 - APIServer - ERROR - 下载文件失败: 'full_path'
2025-06-08 17:29:08 - APIServer - ERROR - 下载文件失败: 'full_path'
2025-06-08 17:29:20 - APIServer - ERROR - 下载文件失败: 'full_path'
2025-06-08 17:29:20 - APIServer - ERROR - 下载文件失败: 'full_path'
2025-06-08 17:45:57 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-08 18:22:24 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-08 18:22:24 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-08 18:23:10 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-08 18:23:10 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-08 18:24:08 - APIServer - ERROR - 获取缩略图失败: [WinError 3] 系统找不到指定的路径。: 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\api\\data\\thumbnails\\medium\\ebfc0a072451aba9096854a34a4c1106_medium.jpg'
2025-06-08 18:24:08 - APIServer - ERROR - 获取缩略图失败: [WinError 3] 系统找不到指定的路径。: 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\api\\data\\thumbnails\\medium\\ebfc0a072451aba9096854a34a4c1106_medium.jpg'
2025-06-08 18:24:11 - APIServer - ERROR - 获取缩略图失败: [WinError 3] 系统找不到指定的路径。: 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\api\\data\\thumbnails\\medium\\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg'
2025-06-08 18:24:11 - APIServer - ERROR - 获取缩略图失败: [WinError 3] 系统找不到指定的路径。: 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\api\\data\\thumbnails\\medium\\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg'
2025-06-08 19:03:11 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 19:29:33 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 20:58:10 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 21:03:13 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 21:17:59 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 22:29:34 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 22:33:13 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 22:33:19 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 23:07:25 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 23:28:32 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 23:41:43 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-10 20:00:43 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:00:43 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:01:57 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:01:57 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:02:50 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:02:50 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:02:54 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:02:54 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:02:56 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:02:59 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:02:59 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:03:01 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:03:01 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 20:03:37 - APIServer - ERROR - 下载文件失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 21:58:38 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-10 22:07:37 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 22:07:37 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 22:09:26 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-11 19:59:11 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-11 20:06:10 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
