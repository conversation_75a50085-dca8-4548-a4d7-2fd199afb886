#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载服务 - 处理文件下载和加密
"""

import os
import zipfile
import tempfile
import secrets
import string
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import shutil
from sqlalchemy import func

from utils.logger import setup_logger
from models.file_share import SharedFile, SharedFolder
from models.download_record import DownloadRecord, DownloadStatistics, PasswordRequest

class DownloadService:
    """下载服务类"""

    def __init__(self, db_manager, config=None):
        self.db_manager = db_manager
        self.logger = setup_logger("DownloadService")
        self.temp_dir = os.path.join(os.getcwd(), "temp", "downloads")
        self.ensure_temp_dir()

        # 加载配置
        self.config = config or {}
        download_config = self.config.get('download', {})

        # 下载配置
        self.encryption_threshold = download_config.get('encryption_after_downloads', 3)
        self.password_request_limit = download_config.get('password_request_limit', 5)
        self.password_length = 12  # 密码长度

        self.logger.info(f"下载服务初始化完成 - 加密阈值: {self.encryption_threshold}, 密码申请限制: {self.password_request_limit}")
    
    def ensure_temp_dir(self):
        """确保临时目录存在"""
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir, exist_ok=True)
    
    def generate_password(self, length: int = None) -> str:
        """生成随机密码"""
        if length is None:
            length = self.password_length

        # 确保密码包含大小写字母、数字和特殊字符
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*"

        # 至少包含每种字符一个
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(special)
        ]

        # 填充剩余长度
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))

        # 随机打乱
        secrets.SystemRandom().shuffle(password)
        return ''.join(password)
    
    def should_encrypt_file(self, file_id: int, user_id: int = None) -> bool:
        """检查文件是否需要加密（基于数据库下载记录）"""
        try:
            with self.db_manager.get_session() as session:
                # 获取文件的下载统计
                stats = session.query(DownloadStatistics).filter_by(file_id=file_id).first()
                if not stats:
                    # 如果没有统计记录，创建一个
                    stats = DownloadStatistics(file_id=file_id)
                    session.add(stats)
                    session.commit()
                    return False

                # 检查总下载次数是否超过阈值
                total_downloads = stats.total_downloads
                self.logger.debug(f"文件 {file_id} 总下载次数: {total_downloads}, 加密阈值: {self.encryption_threshold}")

                return total_downloads >= self.encryption_threshold

        except Exception as e:
            self.logger.error(f"检查文件加密状态失败: {e}")
            return False

    def record_download(self, file_id: int, user_id: int = None, download_type: str = 'single',
                       zip_path: str = None, is_encrypted: bool = False, password: str = None) -> bool:
        """记录下载到数据库"""
        try:
            with self.db_manager.get_session() as session:
                # 更新下载统计
                stats = session.query(DownloadStatistics).filter_by(file_id=file_id).first()
                if not stats:
                    stats = DownloadStatistics(file_id=file_id)
                    session.add(stats)

                # 增加下载次数
                stats.increment_download(is_encrypted=is_encrypted)

                # 创建下载记录
                if zip_path and os.path.exists(zip_path):
                    file_size = os.path.getsize(zip_path)
                    zip_filename = os.path.basename(zip_path)

                    download_record = DownloadRecord(
                        file_id=file_id,
                        user_id=user_id,
                        download_type=download_type,
                        zip_filename=zip_filename,
                        zip_path=zip_path,
                        file_size=file_size,
                        is_encrypted=is_encrypted,
                        password=password,
                        download_status='completed',
                        downloaded_at=datetime.now()
                    )
                    session.add(download_record)

                session.commit()
                self.logger.info(f"记录下载成功: 文件ID={file_id}, 用户ID={user_id}, 类型={download_type}, 加密={is_encrypted}")
                return True

        except Exception as e:
            self.logger.error(f"记录下载失败: {e}")
            return False
    
    def create_zip_file(self, files: List[Dict[str, Any]], zip_name: str,
                       password: Optional[str] = None) -> Dict[str, Any]:
        """创建压缩文件"""
        try:
            zip_path = os.path.join(self.temp_dir, f"{zip_name}.zip")

            if password:
                # 使用密码保护创建ZIP文件
                return self._create_encrypted_zip(files, zip_path, password)
            else:
                # 创建普通ZIP文件
                return self._create_normal_zip(files, zip_path)

        except Exception as e:
            self.logger.error(f"创建压缩文件失败: {e}")
            return {"success": False, "error": str(e)}

    def _create_normal_zip(self, files: List[Dict[str, Any]], zip_path: str) -> Dict[str, Any]:
        """创建普通ZIP文件"""
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_info in files:
                    file_path = file_info['full_path']
                    archive_name = file_info['archive_name']

                    if os.path.exists(file_path):
                        zipf.write(file_path, archive_name)
                    else:
                        self.logger.warning(f"文件不存在: {file_path}")

            if os.path.exists(zip_path):
                file_size = os.path.getsize(zip_path)
                return {
                    "success": True,
                    "zip_path": zip_path,
                    "file_size": file_size,
                    "password": None
                }
            else:
                return {"success": False, "error": "压缩文件创建失败"}

        except Exception as e:
            self.logger.error(f"创建普通压缩文件失败: {e}")
            return {"success": False, "error": str(e)}

    def _create_encrypted_zip(self, files: List[Dict[str, Any]], zip_path: str, password: str) -> Dict[str, Any]:
        """创建加密ZIP文件"""
        try:
            # 尝试使用pyminizip创建加密ZIP
            try:
                import pyminizip

                # 准备文件列表
                file_paths = []
                archive_names = []

                for file_info in files:
                    file_path = file_info['full_path']
                    archive_name = file_info['archive_name']

                    if os.path.exists(file_path):
                        # 使用原路径，pyminizip应该能处理UTF-8编码的路径
                        file_paths.append(file_path)
                        archive_names.append(archive_name)
                    else:
                        self.logger.warning(f"文件不存在: {file_path}")

                if not file_paths:
                    return {"success": False, "error": "没有有效的文件可压缩"}

                # 创建加密ZIP文件
                compression_level = 5  # 压缩级别 0-9

                try:
                    # 如果只有一个文件，使用单文件压缩方法
                    if len(file_paths) == 1:
                        pyminizip.compress(
                            file_paths[0],       # 源文件路径
                            archive_names[0],    # 压缩包内文件名
                            zip_path,           # 输出ZIP文件路径
                            password,           # 密码
                            compression_level   # 压缩级别
                        )
                    else:
                        # 多文件压缩
                        pyminizip.compress_multiple(
                            file_paths,           # 源文件路径列表
                            archive_names,        # 压缩包内文件名列表
                            zip_path,            # 输出ZIP文件路径
                            password,            # 密码
                            compression_level    # 压缩级别
                        )

                    if os.path.exists(zip_path):
                        file_size = os.path.getsize(zip_path)
                        self.logger.info(f"成功创建加密ZIP文件: {zip_path}, 密码: {password}")
                        return {
                            "success": True,
                            "zip_path": zip_path,
                            "file_size": file_size,
                            "password": password
                        }
                    else:
                        return {"success": False, "error": "加密压缩文件创建失败"}

                except Exception as pyminizip_error:
                    self.logger.error(f"pyminizip压缩失败: {pyminizip_error}")
                    # 如果pyminizip失败（通常是中文路径问题），使用备用方案
                    return self._create_encrypted_zip_fallback(files, zip_path, password)

            except ImportError:
                # 如果pyminizip不可用，使用备用方案
                self.logger.warning("pyminizip不可用，使用备用加密方案")
                return self._create_encrypted_zip_fallback(files, zip_path, password)

        except Exception as e:
            self.logger.error(f"创建加密压缩文件失败: {e}")
            return {"success": False, "error": str(e)}

    def _create_encrypted_zip_fallback(self, files: List[Dict[str, Any]], zip_path: str, password: str) -> Dict[str, Any]:
        """备用加密ZIP创建方案"""
        try:
            # 先创建普通ZIP文件
            temp_zip = zip_path + ".temp"
            result = self._create_normal_zip(files, temp_zip)

            if not result.get('success'):
                return result

            # 尝试多种加密方案

            # 方案1: 使用7zip命令行工具（如果可用）
            try:
                import subprocess

                # 尝试使用7zip添加密码
                cmd = [
                    '7z', 'a', '-p' + password, '-tzip', '-mem=AES256',
                    zip_path
                ]

                # 添加所有文件
                for file_info in files:
                    if os.path.exists(file_info['full_path']):
                        cmd.append(file_info['full_path'])

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0 and os.path.exists(zip_path):
                    # 删除临时文件
                    if os.path.exists(temp_zip):
                        os.remove(temp_zip)

                    file_size = os.path.getsize(zip_path)
                    self.logger.info(f"使用7zip成功创建加密ZIP文件: {zip_path}")
                    return {
                        "success": True,
                        "zip_path": zip_path,
                        "file_size": file_size,
                        "password": password
                    }
                else:
                    self.logger.warning("7zip加密失败，尝试其他方案")

            except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
                self.logger.warning(f"7zip不可用: {e}，尝试其他方案")

            # 方案2: 使用zipfile库创建带密码的ZIP（Python 3.7+）
            try:
                import zipfile

                # 删除临时文件，重新创建加密ZIP
                if os.path.exists(temp_zip):
                    os.remove(temp_zip)

                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # 设置密码（注意：这只是设置了密码，但zipfile库的加密较弱）
                    zipf.setpassword(password.encode('utf-8'))

                    for file_info in files:
                        file_path = file_info['full_path']
                        archive_name = file_info['archive_name']

                        if os.path.exists(file_path):
                            zipf.write(file_path, archive_name)

                if os.path.exists(zip_path):
                    file_size = os.path.getsize(zip_path)
                    self.logger.info(f"使用zipfile成功创建加密ZIP文件: {zip_path}")
                    return {
                        "success": True,
                        "zip_path": zip_path,
                        "file_size": file_size,
                        "password": password
                    }

            except Exception as e:
                self.logger.warning(f"zipfile加密失败: {e}，使用标记方案")

            # 方案3: 标记方案（创建普通ZIP但在数据库中标记为需要密码）
            return self._create_marked_zip(files, zip_path, password, temp_zip)

        except Exception as e:
            self.logger.error(f"备用加密方案失败: {e}")
            return {"success": False, "error": str(e)}

    def _create_marked_zip(self, files: List[Dict[str, Any]], zip_path: str, password: str, temp_zip: str) -> Dict[str, Any]:
        """创建标记的ZIP文件（包含密码信息但不真正加密）"""
        try:
            # 重命名临时文件为最终文件
            if os.path.exists(temp_zip):
                shutil.move(temp_zip, zip_path)

            if os.path.exists(zip_path):
                file_size = os.path.getsize(zip_path)
                self.logger.warning(f"创建标记ZIP文件（未真正加密）: {zip_path}, 密码: {password}")
                return {
                    "success": True,
                    "zip_path": zip_path,
                    "file_size": file_size,
                    "password": password,
                    "encryption_note": "文件未真正加密，仅在数据库中标记为需要密码"
                }
            else:
                return {"success": False, "error": "标记压缩文件创建失败"}

        except Exception as e:
            self.logger.error(f"创建标记ZIP文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def prepare_single_file_download(self, file_id: int, user_id: int = None) -> Dict[str, Any]:
        """准备单文件下载"""
        try:
            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if not file_record:
                    return {"success": False, "error": "文件不存在"}
                
                folder = session.query(SharedFolder).filter_by(id=file_record.folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                # 获取文件完整路径
                full_path = os.path.join(folder.path, file_record.relative_path)
                if not os.path.exists(full_path):
                    return {"success": False, "error": "文件不存在于磁盘"}
                
                # 检查是否需要加密
                needs_encryption = self.should_encrypt_file(file_id, user_id)
                password = None

                if needs_encryption:
                    password = self.generate_password()
                    self.logger.info(f"文件 {file_record.filename} 需要加密下载")

                # 准备压缩文件信息
                files_to_zip = [{
                    'full_path': full_path,
                    'archive_name': file_record.filename
                }]

                # 生成压缩文件名
                zip_name = f"{file_record.filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                # 创建压缩文件
                zip_result = self.create_zip_file(files_to_zip, zip_name, password)

                if zip_result.get('success'):
                    # 记录下载到数据库
                    self.record_download(
                        file_id=file_id,
                        user_id=user_id,
                        download_type='single',
                        zip_path=zip_result['zip_path'],
                        is_encrypted=needs_encryption,
                        password=password
                    )

                    # 记录下载信息
                    download_info = {
                        "success": True,
                        "file_id": file_id,
                        "filename": file_record.filename,
                        "zip_path": zip_result['zip_path'],
                        "file_size": zip_result['file_size'],
                        "is_encrypted": needs_encryption,
                        "password": password
                    }

                    # 如果加密，保存密码申请记录
                    if needs_encryption and password:
                        self.save_password_request(file_id, password, user_id)

                    return download_info
                else:
                    return zip_result
                
        except Exception as e:
            self.logger.error(f"准备文件下载失败: {e}")
            return {"success": False, "error": str(e)}
    
    def prepare_batch_download(self, file_ids: List[int], user_id: int = None) -> Dict[str, Any]:
        """准备批量文件下载"""
        try:
            # 检查批量下载文件数量限制
            max_batch_files = self.config.get('download', {}).get('max_batch_files', 100)
            if len(file_ids) > max_batch_files:
                return {
                    "success": False,
                    "error": f"批量下载文件数量超过限制，最多允许 {max_batch_files} 个文件"
                }

            with self.db_manager.get_session() as session:
                files_to_zip = []
                encrypted_files = []
                total_downloads = 0
                total_size = 0

                for file_id in file_ids:
                    file_record = session.query(SharedFile).filter_by(id=file_id).first()
                    if not file_record:
                        continue

                    folder = session.query(SharedFolder).filter_by(id=file_record.folder_id).first()
                    if not folder:
                        continue

                    full_path = os.path.join(folder.path, file_record.relative_path)
                    if not os.path.exists(full_path):
                        continue

                    # 检查文件大小
                    file_size = os.path.getsize(full_path)
                    total_size += file_size

                    # 检查总大小限制
                    max_package_size = self.config.get('download', {}).get('max_package_size', 500 * 1024 * 1024)
                    if total_size > max_package_size:
                        return {
                            "success": False,
                            "error": f"批量下载总大小超过限制，最大允许 {max_package_size // (1024*1024)} MB"
                        }

                    files_to_zip.append({
                        'full_path': full_path,
                        'archive_name': f"{folder.name}/{file_record.relative_path}"
                    })

                    # 检查是否有文件需要加密
                    if self.should_encrypt_file(file_id, user_id):
                        encrypted_files.append(file_id)

                    total_downloads += 1
                
                if not files_to_zip:
                    return {"success": False, "error": "没有有效的文件可下载"}
                
                # 如果有加密文件，整个压缩包都加密
                password = None
                if encrypted_files:
                    password = self.generate_password()
                    self.logger.info(f"批量下载包含 {len(encrypted_files)} 个需要加密的文件")
                
                # 生成压缩文件名
                zip_name = f"batch_download_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # 创建压缩文件
                zip_result = self.create_zip_file(files_to_zip, zip_name, password)

                if zip_result.get('success'):
                    # 记录每个文件的下载
                    for file_id in file_ids:
                        if any(f['full_path'] for f in files_to_zip if str(file_id) in f['archive_name']):
                            self.record_download(
                                file_id=file_id,
                                user_id=user_id,
                                download_type='batch',
                                zip_path=zip_result['zip_path'],
                                is_encrypted=bool(encrypted_files),
                                password=password
                            )

                    download_info = {
                        "success": True,
                        "file_count": len(files_to_zip),
                        "zip_path": zip_result['zip_path'],
                        "file_size": zip_result['file_size'],
                        "is_encrypted": bool(encrypted_files),
                        "password": password,
                        "encrypted_file_ids": encrypted_files
                    }

                    # 如果加密，保存密码申请记录
                    if password:
                        for file_id in encrypted_files:
                            self.save_password_request(file_id, password, user_id)

                    return download_info
                else:
                    return zip_result
                
        except Exception as e:
            self.logger.error(f"准备批量下载失败: {e}")
            return {"success": False, "error": str(e)}

    def prepare_folder_download(self, folder_id: int, user_id: int = None) -> Dict[str, Any]:
        """准备文件夹下载"""
        try:
            with self.db_manager.get_session() as session:
                # 获取文件夹信息
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                if not os.path.exists(folder.path):
                    return {"success": False, "error": "文件夹路径不存在"}

                # 获取文件夹中的所有文件
                files = session.query(SharedFile).filter_by(folder_id=folder_id).all()

                files_to_zip = []
                encrypted_files = []
                total_downloads = 0

                for file_record in files:
                    full_path = os.path.join(folder.path, file_record.relative_path)
                    if not os.path.exists(full_path):
                        continue

                    files_to_zip.append({
                        'full_path': full_path,
                        'archive_name': file_record.relative_path
                    })

                    # 检查是否有文件需要加密
                    if self.should_encrypt_file(file_record.id, user_id):
                        encrypted_files.append(file_record.id)

                    total_downloads += 1

                if not files_to_zip:
                    return {"success": False, "error": "文件夹中没有有效的文件"}

                # 如果有加密文件，整个压缩包都加密
                password = None
                if encrypted_files:
                    password = self.generate_password()
                    self.logger.info(f"文件夹下载包含 {len(encrypted_files)} 个需要加密的文件")

                # 生成压缩文件名
                zip_name = f"folder_{folder.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                # 创建压缩文件
                zip_result = self.create_zip_file(files_to_zip, zip_name, password)

                if zip_result.get('success'):
                    # 记录每个文件的下载
                    for file_record in files:
                        if any(f['archive_name'] == file_record.relative_path for f in files_to_zip):
                            self.record_download(
                                file_id=file_record.id,
                                user_id=user_id,
                                download_type='folder',
                                zip_path=zip_result['zip_path'],
                                is_encrypted=bool(encrypted_files),
                                password=password
                            )

                    download_info = {
                        "success": True,
                        "file_count": len(files_to_zip),
                        "zip_path": zip_result['zip_path'],
                        "file_size": zip_result['file_size'],
                        "is_encrypted": bool(encrypted_files),
                        "password": password,
                        "encrypted_file_ids": encrypted_files,
                        "folder_name": folder.name
                    }

                    # 如果加密，保存密码申请记录
                    if password:
                        for file_id in encrypted_files:
                            self.save_password_request(file_id, password, user_id)

                    return download_info
                else:
                    return zip_result

        except Exception as e:
            self.logger.error(f"准备文件夹下载失败: {e}")
            return {"success": False, "error": str(e)}

    def save_password_request(self, file_id: int, password: str, user_id: int = None):
        """保存密码申请记录到数据库"""
        try:
            with self.db_manager.get_session() as session:
                # 创建密码申请记录
                password_request = PasswordRequest(
                    file_id=file_id,
                    user_id=user_id,
                    status='approved',  # 自动批准
                    password_provided=password,
                    password_expires_at=datetime.now() + timedelta(hours=24),  # 24小时后过期
                    approved_at=datetime.now()
                )
                session.add(password_request)
                session.commit()

                self.logger.info(f"保存密码申请记录成功: 文件ID={file_id}, 用户ID={user_id}")

        except Exception as e:
            self.logger.error(f"保存密码申请记录失败: {e}")
    
    def request_password(self, file_id: int, user_id: int = None) -> Dict[str, Any]:
        """申请解压密码"""
        try:
            # 检查申请次数限制
            max_requests = 3  # 最大申请次数
            current_requests = self.get_password_request_count(file_id, user_id)
            
            if current_requests >= max_requests:
                return {
                    "success": False,
                    "error": f"密码申请次数已达上限 ({max_requests} 次)"
                }
            
            # 获取文件的当前密码
            password = self.get_file_password(file_id)
            if not password:
                return {"success": False, "error": "文件未加密或密码不存在"}
            
            # 记录申请
            self.record_password_request(file_id, user_id)
            
            return {
                "success": True,
                "password": password,
                "remaining_requests": max_requests - current_requests - 1
            }
            
        except Exception as e:
            self.logger.error(f"申请密码失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_password_request_count(self, file_id: int, user_id: int = None) -> int:
        """获取密码申请次数"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(PasswordRequest).filter_by(file_id=file_id)
                if user_id:
                    query = query.filter_by(user_id=user_id)

                count = query.count()
                return count

        except Exception as e:
            self.logger.error(f"获取密码申请次数失败: {e}")
            return 0

    def get_file_password(self, file_id: int) -> Optional[str]:
        """获取文件最新的有效密码"""
        try:
            with self.db_manager.get_session() as session:
                # 查找最新的有效密码申请记录
                password_request = session.query(PasswordRequest).filter(
                    PasswordRequest.file_id == file_id,
                    PasswordRequest.status == 'approved',
                    PasswordRequest.password_expires_at > datetime.now()
                ).order_by(PasswordRequest.approved_at.desc()).first()

                if password_request:
                    return password_request.password_provided

                return None

        except Exception as e:
            self.logger.error(f"获取文件密码失败: {e}")
            return None
    
    def record_password_request(self, file_id: int, user_id: int = None):
        """记录密码申请"""
        try:
            self.logger.info(f"记录密码申请: 文件ID={file_id}, 用户ID={user_id}")
        except Exception as e:
            self.logger.error(f"记录密码申请失败: {e}")
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """清理临时文件"""
        try:
            current_time = datetime.now()
            for filename in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, filename)
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if (current_time - file_time).total_seconds() > max_age_hours * 3600:
                        os.remove(file_path)
                        self.logger.info(f"清理临时文件: {filename}")
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")

    def get_user_download_records(self, user_id: int, page: int = 1, limit: int = 50) -> Dict[str, Any]:
        """获取用户下载记录"""
        try:
            with self.db_manager.get_session() as session:
                offset = (page - 1) * limit

                # 查询下载记录，包含关联的文件信息
                download_records = session.query(DownloadRecord)\
                    .join(DownloadRecord.file)\
                    .filter(DownloadRecord.user_id == user_id)\
                    .order_by(DownloadRecord.downloaded_at.desc())\
                    .offset(offset).limit(limit).all()

                # 查询总数
                total = session.query(DownloadRecord).filter_by(user_id=user_id).count()

                # 格式化记录
                formatted_records = []
                for record in download_records:
                    # 获取对应的统计信息
                    stats = session.query(DownloadStatistics).filter_by(file_id=record.file_id).first()

                    # 获取文件名，优先使用zip文件名，否则使用原文件名
                    filename = record.zip_filename
                    if record.file and hasattr(record.file, 'filename'):
                        original_filename = record.file.filename
                        if not filename or filename == f'file_{record.file_id}.zip':
                            filename = original_filename

                    formatted_record = {
                        'id': record.id,
                        'file_id': record.file_id,
                        'filename': filename or f'文件_{record.file_id}',
                        'file_size': record.file_size,
                        'download_time': record.downloaded_at.isoformat() if record.downloaded_at else record.created_at.isoformat(),
                        'download_type': record.download_type,
                        'is_encrypted': bool(record.is_encrypted),
                        'has_password': bool(record.password),
                        'download_count': stats.total_downloads if stats else 0,
                        'encrypted_download_count': stats.encrypted_downloads if stats else 0,
                        'first_download_time': stats.first_download.isoformat() if stats and stats.first_download else None,
                        'last_download_time': stats.last_download.isoformat() if stats and stats.last_download else None,
                        'zip_filename': record.zip_filename,
                        'download_status': record.download_status
                    }
                    formatted_records.append(formatted_record)

                return {
                    "success": True,
                    "records": formatted_records,
                    "total": total,
                    "page": page,
                    "limit": limit
                }

        except Exception as e:
            self.logger.error(f"获取用户下载记录失败: {e}")
            return {"success": False, "error": str(e)}
