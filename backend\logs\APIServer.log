2025-06-11 19:35:25 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-11 19:47:55 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:47:55 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:47:55 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:47:55 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:47:55 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:47:55 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:47:55 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:47:55 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:47:55 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:47:55 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:47:55 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:47:55 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:51:22 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:51:22 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:51:22 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:51:54 - APIServer - INFO - API服务器已停止
2025-06-11 19:52:03 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-11 19:52:07 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:52:07 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:52:07 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:52:07 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:52:07 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:52:07 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:52:07 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:52:07 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:52:07 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:52:07 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:52:07 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:52:07 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:53:53 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:53:53 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:53:53 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:53:53 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:53:53 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:53:53 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:53:53 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:53:53 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:53:53 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:53:53 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:53:53 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:53:53 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:54:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': '2025-06-10T20:19:25'}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-11 19:54:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-11 19:54:21 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-11 19:54:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-11 19:54:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-11 19:54:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-11 19:54:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': '2025-06-10T20:19:25'}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-11 19:54:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-11 19:54:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-11 19:54:21 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-11 19:54:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-11 19:54:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-11 19:54:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-11 19:59:11 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-11 19:59:11 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-11 19:59:42 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 19:59:42 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 19:59:42 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 19:59:55 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-11 20:00:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:00:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:00:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:00:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:00:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:00:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:00:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:00:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:00:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:00:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:00:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:00:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:00:18 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:00:18 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:00:18 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:00:18 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:00:18 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:00:18 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:00:18 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:00:18 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:00:18 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:00:18 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:00:18 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:00:18 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:01:20 - APIServer - INFO - API服务器已停止
2025-06-11 20:01:39 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-11 20:01:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:01:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:01:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:01:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:01:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:01:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:01:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:01:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:01:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-11 20:01:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-11 20:01:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-11 20:01:43 - APIServer - INFO - 返回 3 个文件夹
